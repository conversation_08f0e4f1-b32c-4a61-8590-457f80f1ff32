:root {
  --padi-green: rgb(88, 194, 52);
  --padi-green-dark: #58b530;
  --padi-orange: rgb(237, 160, 36);
  --primary-black: #0a0a0a;
  --secondary-black: #1a1a1a;
  --accent-gray: #2a2a2a;
  --light-gray: #f5f5f5;
  --white: #ffffff;
  --danger: #e74c3c;
  --navbar-height: 72px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: var(--primary-black);
  background: var(--light-gray);
}

/* Navbar */
.navbar {
  background: rgba(0, 0, 0, 0.18);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  height: var(--navbar-height);
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--white);
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: var(--white);
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: var(--padi-orange);
}

/* Container */
.container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 0 1rem;
}

.admin-container {
  padding: calc(2rem + var(--navbar-height)) 0 2rem;
  min-height: 100vh;
}

/* Header */
.admin-header {
  margin-bottom: 3rem;
  text-align: center;
}

.admin-header h1 {
  font-size: 2.5rem;
  color: var(--primary-black);
  margin-bottom: 0.5rem;
}

.admin-header .subtitle {
  font-size: 1.1rem;
  color: rgba(0, 0, 0, 0.6);
}

/* Form Section */
.form-section {
  background: var(--white);
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 3rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.form-section h2 {
  font-size: 1.5rem;
  color: var(--primary-black);
  margin-bottom: 1.5rem;
  border-bottom: 2px solid var(--padi-green);
  padding-bottom: 0.5rem;
}

.product-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--primary-black);
  font-size: 0.95rem;
}

.form-group input,
.form-group select {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-family: inherit;
  font-size: 0.95rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--padi-green);
  box-shadow: 0 0 0 3px rgba(88, 194, 52, 0.1);
}

.form-group input[type="checkbox"] {
  width: auto;
  margin-right: 0.5rem;
  cursor: pointer;
}

.form-group label:has(input[type="checkbox"]) {
  flex-direction: row;
  align-items: center;
  margin-bottom: 0;
}

/* Buttons */
.btn-primary,
.btn-secondary,
.btn-danger {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.btn-primary {
  background: var(--padi-green);
  color: var(--white);
}

.btn-primary:hover {
  background: var(--padi-green-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(88, 194, 52, 0.3);
}

.btn-secondary {
  background: #e0e0e0;
  color: var(--primary-black);
}

.btn-secondary:hover {
  background: #d0d0d0;
}

.btn-danger {
  background: var(--danger);
  color: var(--white);
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
}

.btn-danger:hover {
  background: #c0392b;
}

/* Products Section */
.products-section {
  background: var(--white);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.products-section h2 {
  font-size: 1.5rem;
  color: var(--primary-black);
  margin-bottom: 1.5rem;
  border-bottom: 2px solid var(--padi-orange);
  padding-bottom: 0.5rem;
}

.products-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.products-controls input,
.products-controls select {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-family: inherit;
  font-size: 0.95rem;
}

.products-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.product-item {
  display: grid;
  grid-template-columns: 100px 1fr auto;
  gap: 1.5rem;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.product-item:hover {
  border-color: var(--padi-green);
  background: #f5f5f5;
}

.product-image {
  width: 100px;
  height: 100px;
  border-radius: 6px;
  background: #e0e0e0;
  background-size: cover;
  background-position: center;
}

.product-info h3 {
  font-size: 1.1rem;
  color: var(--primary-black);
  margin-bottom: 0.25rem;
}

.product-info p {
  font-size: 0.9rem;
  color: rgba(0, 0, 0, 0.6);
  margin: 0.25rem 0;
}

.product-price {
  font-weight: 700;
  color: var(--padi-green);
  font-size: 1.1rem;
}

.product-badge {
  display: inline-block;
  background: var(--padi-orange);
  color: var(--white);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-top: 0.25rem;
}

.product-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-edit {
  background: var(--padi-orange);
  color: var(--white);
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.3s ease;
}

.btn-edit:hover {
  background: #d4941f;
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  align-items: center;
  justify-content: center;
}

.modal.active {
  display: flex;
}

.modal-content {
  background: var(--white);
  padding: 2rem;
  border-radius: 12px;
  max-width: 400px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-content h3 {
  font-size: 1.3rem;
  color: var(--primary-black);
  margin-bottom: 1rem;
}

.modal-content p {
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 1.5rem;
}

.modal-buttons {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.modal-buttons button {
  flex: 1;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 2rem;
  color: rgba(0, 0, 0, 0.6);
}

.empty-state p {
  font-size: 1.1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .product-item {
    grid-template-columns: 80px 1fr;
  }

  .product-actions {
    grid-column: 1 / -1;
    margin-top: 1rem;
  }

  .admin-header h1 {
    font-size: 1.8rem;
  }

  .nav-links {
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .form-section,
  .products-section {
    padding: 1.5rem;
  }

  .products-controls {
    flex-direction: column;
  }

  .product-item {
    grid-template-columns: 1fr;
  }

  .product-image {
    width: 100%;
    height: 150px;
  }
}

