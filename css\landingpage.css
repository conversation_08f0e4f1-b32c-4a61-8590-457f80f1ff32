:root {
  --primary-black: #0a0a0a;
  --secondary-black: #1a1a1a;
  --accent-gray: #2a2a2a;
  --light-gray: #f5f5f5;
  --white: #ffffff;
  --gold-accent: #d4af37;
  /* Brand colors for PADI MART */
  /* Updated brand colors per request */
  --padi-green: rgb(88, 194, 52); /* hijau muda */
  --padi-green-dark: #58b530; /* slightly darker */
  --padi-orange: rgb(237, 160, 36); /* oranye */
  --navbar-height: 72px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: var(--white);
  overflow-x: hidden;
}


/* Do not add global top padding — hero should sit under the fixed navbar. */
/* Add top padding only to non-hero sections so content isn't hidden behind navbar */
.section:not(.hero-section) { padding-top: calc(4rem + var(--navbar-height)); }

/* Utility containers */
.container { max-width: 1100px; margin: 0 auto; padding: 0 1rem; }
.section { padding: 4rem 0; color: var(--primary-black); background: transparent; }
.muted { color: rgba(0,0,0,0.6); }

/* Specific section backgrounds */
#about { background: var(--white); }
#products { background: var(--white); }

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  background: transparent; /* default transparent over hero */
  backdrop-filter: blur(6px);
  transition: all 0.25s ease;
  padding: 0.6rem 1rem;
}

.navbar .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* scrolled state (subtle blur, not solid white) */
.navbar.scrolled {
  /* keep it subtle and translucent so hero still reads through */
  background: rgba(0,0,0,0.20);
  backdrop-filter: blur(8px);
  box-shadow: 0 6px 18px rgba(0,0,0,0.06);
}
.navbar.scrolled .nav-link { color: var(--white); }
.navbar.scrolled .brand-text { color: var(--white); }
.navbar.scrolled .hamburger-line { background: var(--white); }

.navbar-brand { display:flex; align-items:center; gap:0.6rem; text-decoration:none; }
.brand-logo { height:36px; width:auto; display:inline-block; }
.brand-logo { max-height:40px; object-fit:contain; }
.brand-text { font-weight:700; color:var(--white); font-size:1rem; white-space:nowrap; }

.left-group { display:flex; align-items:center; gap:0.6rem; margin-left:0.2rem; }
.right-group { display:flex; align-items:center; gap:0.8rem; }
.main-nav { margin-left: 1.5rem; display:flex; gap:1rem; }

/* Ensure nav sits on the right side and collapses gracefully */
.right-group { margin-left: auto; display:flex; align-items:center; }
.main-nav { display:flex; gap:1.25rem; align-items:center; }
.main-nav .nav-link { padding: 0.4rem 0.2rem; }

/* prevent nav overlapping hero on small screens */
@media (max-width: 768px) {
  .brand-text { display: none; }
  .brand-logo { height:32px; }
  .navbar { padding: 0.5rem 0.75rem; }
  .hero-section { align-items: center; }
  :root { --navbar-height: 56px; }
  /* don't add global padding here; non-hero sections get padding instead */
}

.nav-logo { margin-left: 1rem; }

.navbar-brand {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--white) !important;
  text-decoration: none;
  margin: 0;
}

/* Hamburger Menu Button */
.hamburger-menu {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 4px;
  z-index: 1001;
}

/* hide hamburger on desktop */
@media (min-width: 769px) {
  .hamburger-menu { display: none; }
  .brand-text { display: inline-block; }
}

.hamburger-line {
  width: 24px;
  height: 2px;
  background: var(--white);
  transition: all 0.3s ease;
  border-radius: 2px;
}

.hamburger-menu:hover .hamburger-line {
  background: var(--gold-accent);
}

/* Main Navigation */
.main-nav {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.main-nav .nav-link {
  color: var(--white);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.98rem;
  transition: color 0.2s ease;
  position: relative;
  display: inline-flex; align-items:center; gap:0.5rem;
}

.main-nav .nav-link:hover {
  color: var(--gold-accent);
}

.main-nav .nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gold-accent);
  transition: width 0.3s ease;
}

.main-nav .nav-link:hover::after {
  width: 100%;
}

/* Side Menu Overlay */
.side-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1998;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.side-menu-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Side Menu */
.side-menu {
  position: fixed;
  top: 0;
  left: -400px;
  width: 400px;
  height: 100%;
  background: var(--white);
  z-index: 1999;
  transition: left 0.3s ease;
  overflow-y: auto;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
}

.side-menu.active {
  left: 0;
}

.side-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e5e5;
  background: #f8f8f8;
}

.side-menu-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-black);
}

.close-menu {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  color: var(--primary-black);
  transition: color 0.3s ease;
}

.close-menu:hover {
  color: var(--gold-accent);
}

.side-menu-content {
  padding: 2rem;
}

.menu-section {
  margin-bottom: 2.5rem;
}

.menu-section h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-black);
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-bottom: 2px solid var(--gold-accent);
  padding-bottom: 0.5rem;
}

.menu-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu-list li {
  margin-bottom: 0.5rem;
}

.menu-list a {
  display: block;
  padding: 0.75rem 0;
  color: var(--primary-black);
  text-decoration: none;
  font-weight: 400;
  font-size: 1rem;
  transition: all 0.3s ease;
  border-radius: 4px;
  padding-left: 1rem;
}

.menu-list a:hover {
  background: #f0f0f0;
  color: var(--gold-accent);
  padding-left: 1.5rem;
}

/* responsivny navibar */
@media (max-width: 768px) {
  .main-nav {
    display: none;
  }
  .hamburger-menu { display:block; }
  .navbar-brand .brand-text { font-size: 0.95rem; }

  .side-menu {
    width: 320px;
    left: -320px;
  }

  .navbar-brand {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .side-menu {
    width: 280px;
    left: -280px;
  }

  .side-menu-content {
    padding: 1.5rem;
  }

  .navbar-brand {
    font-size: 1.3rem;
  }
}

/* Hero Section */
.hero-section {
  height: 100vh; /* full viewport height */
  background: linear-gradient(
    135deg,
    var(--primary-black) 0%,
    var(--secondary-black) 100%
  );
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../img/hero-bg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.3;
  animation: slowZoom 20s ease-in-out infinite alternate;
}

@keyframes slowZoom {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.05);
  }
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    rgba(20, 20, 20, 0.12) 0%,
    rgba(26, 26, 26, 0.5) 100%
  );
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: left;
  max-width: 820px;
  padding: 1rem 2rem 4rem 2rem;
  margin-left: 3rem;
  animation: fadeInUp 1.2s ease-out;
}

/* responsive hero layout */
@media (max-width: 1024px) {
  .hero-content { max-width: 720px; padding: 1rem 2rem; }
}

@media (max-width: 768px) {
  .hero-section { height: 100vh; align-items: center; }
  .hero-content { margin-left: 0; text-align: center; padding: 1rem; max-width: 100%; }
  .hero-title { font-size: clamp(1.6rem, 6vw, 2.6rem); }
  .hero-description { font-size: 1rem; }
  .scroll-indicator { display: none; }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-subtitle {
  font-size: 1.1rem;
  font-weight: 300;
  color: var(--gold-accent);
  margin-bottom: 1rem;
  letter-spacing: 2px;
  text-transform: uppercase;
  animation: fadeInUp 1.2s ease-out 0.2s both;
}

.hero-title {
  font-size: clamp(1.8rem, 6.2vw, 3.8rem); /* slightly larger to match shorter title */
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.02;
  animation: fadeInUp 1.2s ease-out 0.4s both;
}

.hero-description {
  font-size: 1.3rem;
  font-weight: 300;
  margin-bottom: 3rem;
  color: rgba(255, 255, 255, 0.8);
  max-width: 500px;
  animation: fadeInUp 1.2s ease-out 0.6s both;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: flex-start;
  flex-wrap: wrap;
  animation: fadeInUp 1.2s ease-out 0.8s both;
}

.btn-primary-custom {
  background: var(--padi-green);
  color: var(--primary-black);
  border: none;
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-radius: 0;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.btn-primary-custom:hover {
  background: var(--padi-green-dark);
  color: var(--primary-black);
  transform: translateY(-2px);
  box-shadow: 0 30px 30px rgba(0, 0, 0, 0.3);
}

.btn-secondary-custom {
  background: transparent;
  color: var(--white);
  border: 2px solid var(--white);
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-radius: 0;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.btn-secondary-custom:hover {
  background: var(--white);
  color: var(--primary-black);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
}

/* Scroll indicator */
.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  color: var(--white);
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    padding: 0 1rem 2rem 1rem;
    margin-left: 0;
    text-align: center;
    max-width: 100%;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .btn-primary-custom,
  .btn-secondary-custom {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .hero-content {
    padding: 0 1rem 1rem 1rem;
  }
}

/* Button Container Styles */
.button-container {
  display: flex;
  gap: 1rem;
  width: 100vw;
  margin: 0;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  padding: 0 2rem;
  box-sizing: border-box;
}

.btn-left {
  flex: 0 0 60%;
  background: #20BF1D;
  color: var(--white);
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-radius: 4px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-left:hover {
  background: #1a9e18;
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(32, 191, 29, 0.3);
}

.btn-right {
  flex: 0 0 calc(40% - 1rem);
  background: #F37E29;
  color: var(--white);
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-radius: 4px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-right:hover {
  background: #d96a1f;
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(243, 126, 41, 0.3);
}

/* About grid */
.about-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; align-items: center; }
.about-text h2 { margin-bottom: 0.5rem; }
.video-wrapper { position: relative; padding-top: 56.25%; border-radius: 8px; overflow: hidden; box-shadow: 0 8px 30px rgba(0,0,0,0.08); }
.video-wrapper iframe { position: absolute; top:0; left:0; width:100%; height:100%; }

/* Product controls */
.product-controls { display:flex; gap:1rem; align-items:center; margin:1.5rem 0 2rem; }
.product-controls input[type="search"] { flex:1; padding:0.8rem 1rem; border-radius:8px; border:1px solid #ddd; }
.product-controls select { padding:0.7rem 0.8rem; border-radius:8px; border:1px solid #ddd; background:#fff; }

/* Product grid (Shopee-style card) */
.product-grid { display:grid; grid-template-columns: repeat(4, 1fr); gap:1rem; }
.product-card { background: var(--white); border-radius:14px; overflow:hidden; box-shadow:0 10px 30px rgba(0,0,0,0.06); color:var(--primary-black); display:flex; flex-direction:column; position:relative; }
.product-card { transition: transform 0.25s ease, box-shadow 0.25s ease; }
.product-card:hover { transform: translateY(-6px); box-shadow: 0 18px 45px rgba(0,0,0,0.12); }
.product-thumb { display:none; }

/* Card UX inspired by reference */
.product-card .card__shine { position:absolute; inset:0; background:linear-gradient(120deg, rgba(255,255,255,0) 40%, rgba(255,255,255,0.8) 50%, rgba(255,255,255,0) 60%); opacity:0; transition: opacity 0.3s ease; pointer-events:none; }
.product-card .card__glow { position:absolute; inset:-10px; background:radial-gradient(circle at 50% 0%, rgba(88,194,52,0.12) 0%, rgba(88,194,52,0) 70%); opacity:0; transition: opacity 0.5s ease; pointer-events:none; }
.product-card .card__content { padding:1rem; height:100%; display:flex; flex-direction:column; gap:0.5rem; position:relative; z-index:2; }
.product-card .card__badge { position:absolute; top:12px; right:12px; background: var(--padi-green); color:#fff; padding:0.25em 0.5em; border-radius:999px; font-size:0.7em; font-weight:600; transform:scale(0.85); opacity:0; transition: all 0.4s ease 0.1s; }
.product-card .card__image { width:100%; height:140px; background-size:cover; background-position:center; border-radius:12px; transition: all 0.5s cubic-bezier(0.16,1,0.3,1); position:relative; overflow:hidden; }
.product-card .card__text { display:flex; flex-direction:column; gap:0.25em; }
.product-card .card__title { color: #111827; font-size:1rem; margin:0; font-weight:700; transition: all 0.3s ease; }
.product-card .card__description { color:#374151; font-size:0.85rem; margin:0; opacity:0.85; transition: all 0.3s ease; }
.product-card .card__footer { display:flex; justify-content:space-between; align-items:center; margin-top:auto; }
.product-card .card__price { color: #111827; font-weight:800; font-size:1rem; transition: all 0.3s ease; }
.product-card .card__button { width:32px; height:32px; background: var(--padi-green); border-radius:50%; display:flex; align-items:center; justify-content:center; color:#fff; cursor:pointer; transition: all 0.3s ease; transform: scale(0.95); }

/* hover */
.product-card:hover { transform: translateY(-12px); box-shadow: 0 28px 60px rgba(16, 185, 129, 0.08), 0 10px 20px rgba(0,0,0,0.08); border-color: rgba(88,194,52,0.12); }
.product-card:hover .card__shine { opacity:1; animation: shine 3s infinite; }
.product-card:hover .card__glow { opacity:1; }
.product-card:hover .card__badge { transform:scale(1); opacity:1; }
.product-card:hover .card__image { transform: translateY(-6px) scale(1.03); box-shadow: 0 12px 20px rgba(0,0,0,0.08); }
.product-card:hover .card__title { color: var(--padi-green); transform: translateX(2px); }
.product-card:hover .card__description { opacity:1; transform: translateX(2px); }
.product-card:hover .card__price { color: var(--padi-orange); transform: translateX(2px); }
.product-card:hover .card__button { transform: scale(1); box-shadow: 0 0 0 6px rgba(88,194,52,0.12); }
.product-body { padding:0.75rem 1rem; flex:1; display:flex; flex-direction:column; }
.product-title { font-weight:600; margin-bottom:0.25rem; font-size:0.95rem; }
.product-meta { font-size:0.9rem; color:rgba(0,0,0,0.6); margin-bottom:0.5rem; }
.product-footer { display:flex; align-items:center; justify-content:space-between; padding:0.75rem 1rem; border-top:1px solid #f0f0f0; }
.price { font-weight:700; color:var(--padi-orange); }
.whatsapp-cta { background:var(--padi-green); color:#fff; padding:0.5rem 0.75rem; border-radius:6px; text-decoration:none; font-weight:600; }
.whatsapp-cta:hover { transform: translateY(-3px); box-shadow: 0 12px 24px rgba(32,191,29,0.18); }

/* Porsche-like overlay effect for cards: on hover show translucent overlay and CTA */
.product-card { position: relative; overflow: hidden; }
.product-overlay { position: absolute; inset: 0; background: linear-gradient(180deg, rgba(0,0,0,0.0) 30%, rgba(0,0,0,0.6) 100%); opacity: 0; transition: opacity 0.35s ease; display:flex; align-items:flex-end; padding:1rem; }
.product-card:hover .product-overlay { opacity: 1; }
.product-overlay .overlay-meta { color: #fff; display:flex; align-items:center; justify-content:space-between; width:100%; }
.overlay-price { font-weight:800; font-size:1rem; color: var(--padi-orange); }
.overlay-cta { background: rgba(255,255,255,0.12); padding:0.5rem 0.75rem; border-radius:6px; color:#fff; text-decoration:none; font-weight:700; }
.overlay-cta:hover { background: rgba(255,255,255,0.18); }

/* Testimonials */
.testimonials-grid { display:grid; grid-template-columns: repeat(3,1fr); gap:1rem; }
.testimonial { background:#fff; padding:1rem; border-radius:8px; box-shadow:0 6px 20px rgba(0,0,0,0.05); color:var(--primary-black); }
/* testimonial slider styles */
.testimonial-slider { position: relative; display:flex; align-items:center; }
.slider-track { display:flex; gap:1rem; overflow:hidden; width:100%; }
.slider-track .testimonial { min-width: 100%; box-sizing:border-box; }
.slider-btn { background: rgba(0,0,0,0.05); border: none; width:44px; height:44px; border-radius:8px; cursor:pointer; font-size:1.5rem; display:flex; align-items:center; justify-content:center; }
.slider-btn:hover { background: rgba(0,0,0,0.08); }
.prev { margin-right: 1rem; }
.next { margin-left: 1rem; }

/* Footer */
.footer { background: #f8f8f8; padding:2.5rem 0; color:var(--primary-black); }
.footer-grid { display:grid; grid-template-columns: repeat(3,1fr); gap:1rem; }

/* Removed earlier duplicate WA button blocks to avoid conflicts. Canonical Uiverse rules are in the final overrides below. */


/* Responsive adjustments */
@media (max-width: 1024px) {
  .product-grid { grid-template-columns: repeat(3,1fr); }
}

@media (max-width: 768px) {
  .about-grid { grid-template-columns: 1fr; }
  .product-grid { grid-template-columns: repeat(2,1fr); }
  .testimonials-grid { grid-template-columns: 1fr; }
}

@media (max-width: 480px) {
  .product-grid { grid-template-columns: 1fr; }
  .product-controls { flex-direction:column; align-items:stretch; }
}

/* FINAL OVERRIDES: enforce translucent navbar and canonical Uiverse WhatsApp styles
   These rules are placed last to override any duplicated blocks created during iterative edits. */

/* Force navbar to remain translucent/blurred (not white) */
.navbar,
.navbar.scrolled {
  background: rgba(0, 0, 0, 0.18) !important; /* subtle dark translucent overlay */
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  box-shadow: none !important;
  color: var(--white) !important;
}
.navbar.scrolled .nav-link,
.navbar .nav-link { color: var(--white) !important; }
.navbar.scrolled .brand-text,
.navbar .brand-text { color: var(--white) !important; }

/* Slightly larger logo and nudge to the far left */
.brand-logo { height: 44px !important; max-height: 48px !important; }
.left-group { margin-left: 4px !important; }
.navbar .container { padding-left: 10px !important; padding-right: 10px !important; }

/* Hide any legacy floating WA button definitions left in the file */
.whatsapp-float { display: none !important; opacity: 0 !important; pointer-events: none !important; }

/* Canonical Uiverse WhatsApp button (final authoritative rules) */
:root { --uiv-whatsapp-size: 56px; }
.uiverse-whatsapp { position: fixed !important; right: 18px !important; bottom: 18px !important; z-index: 99999 !important; text-decoration: none !important; }
.uiverse-whatsapp .Btn { width: var(--uiv-whatsapp-size) !important; height: var(--uiv-whatsapp-size) !important; border-radius: 999px !important; display:flex !important; align-items:center !important; gap:8px !important; padding:6px !important; background: linear-gradient(180deg, var(--padi-green) 0%, var(--padi-green-dark) 100%) !important; box-shadow: 0 10px 30px rgba(0,0,0,0.22) !important; color:#fff !important; transition: all 0.28s ease !important; overflow: hidden !important; }
.uiverse-whatsapp .Btn .sign { display:flex !important; align-items:center !important; justify-content:center !important; width: calc(var(--uiv-whatsapp-size) - 28px) !important; height: calc(var(--uiv-whatsapp-size) - 28px) !important; background: transparent !important; border-radius: 999px !important; flex: 0 0 auto !important; }
.uiverse-whatsapp .Btn .sign { display:flex !important; align-items:center !important; justify-content:center !important; background: transparent !important; border-radius: 999px !important; flex: 1 1 auto !important; width: auto !important; height: calc(var(--uiv-whatsapp-size) - 12px) !important; }
.uiverse-whatsapp:hover .Btn .sign,
.uiverse-whatsapp:focus .Btn .sign { flex: 0 0 calc(var(--uiv-whatsapp-size) - 28px) !important; width: calc(var(--uiv-whatsapp-size) - 28px) !important; }
.uiverse-whatsapp .socialSvg { width: 20px !important; height: 20px !important; fill: #ffffff !important; display:block !important; margin: 0 auto !important; }
.uiverse-whatsapp .Btn .text { display:none !important; font-weight:700 !important; font-size:0.95rem !important; white-space:nowrap !important; }
.uiverse-whatsapp:hover .Btn { width: calc(var(--uiv-whatsapp-size) * 1.6) !important; padding: 8px 12px !important; }
.uiverse-whatsapp:hover .Btn .text { display:block !important; margin-left:6px !important; }

/* Ensure accessibility: focus shows expanded state too */
.uiverse-whatsapp:focus .Btn,
.uiverse-whatsapp .Btn:focus { width: calc(var(--uiv-whatsapp-size) * 1.6) !important; }

/* Mobile: keep compact; Desktop: show text on hover/focus (handled via :hover and :focus rules) */


/* Large desktop tweak to make hero title match screenshot scale */
@media (min-width: 1200px) {
  .hero-title { font-size: clamp(2.2rem, 5.8vw, 3.4rem); }
  .hero-content { margin-left: 4rem; max-width: 900px; }
}

/* Auto-hide transform for navbar */
.navbar.hidden { transform: translateY(-120%); transition: transform 0.28s cubic-bezier(.22,.9,.31,1); }
.navbar.hidden { transform: translateY(-120%); transition: transform 0.28s cubic-bezier(.22,.9,.31,1); }

/* Final override: ensure only Uiverse button appears and legacy .whatsapp-float doesn't override it */
.whatsapp-float { display: none !important; }
.uiverse-whatsapp { display: inline-block !important; position: fixed !important; right: 20px !important; bottom: 20px !important; z-index: 2000 !important; }
.uiverse-whatsapp::after { display: block; }

/* Authoritative final behavior: expand to fit text (avoid truncation) */
.uiverse-whatsapp .Btn { overflow: visible !important; min-width: var(--uiv-whatsapp-size) !important; }
.uiverse-whatsapp .Btn .text { display: none !important; }
.uiverse-whatsapp:hover .Btn,
.uiverse-whatsapp:focus .Btn {
  width: auto !important;
  min-width: calc(var(--uiv-whatsapp-size) * 2.2) !important; /* ensure enough room for the text */
  padding: 8px 14px !important;
}
.uiverse-whatsapp:hover .Btn .text,
.uiverse-whatsapp:focus .Btn .text { display: block !important; margin-left: 8px !important; }

/* FINAL: force navbar to be visually transparent and remove any top white strip */
.navbar, nav.navbar, header nav.navbar, .navbar.scrolled {
  background-color: transparent !important;
  background: transparent !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  box-shadow: none !important;
  border-bottom: none !important;
}
.navbar, .navbar * { background: transparent !important; }
.navbar .container { background: transparent !important; padding-top: 6px !important; padding-bottom: 6px !important; }


/* FINAL: ensure whatsapp icon is vertically centered in its sign container */
.uiverse-whatsapp .Btn { display:flex !important; align-items:center !important; justify-content:center !important; height: var(--uiv-whatsapp-size) !important; }
.uiverse-whatsapp:hover .Btn, .uiverse-whatsapp:focus .Btn { justify-content:flex-start !important; }
.uiverse-whatsapp .Btn .sign { display:flex !important; align-items:center !important; justify-content:center !important; flex: 0 0 auto !important; width: calc(var(--uiv-whatsapp-size) - 12px) !important; height: calc(var(--uiv-whatsapp-size) - 12px) !important; }
.uiverse-whatsapp .Btn .text { margin-left: 10px !important; }
.uiverse-whatsapp .socialSvg { display:block !important; margin: 0 auto !important; }




/* About grid */
.about-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; align-items: center; }
.about-text h2 { margin-bottom: 0.5rem; }
.video-wrapper { position: relative; padding-top: 56.25%; border-radius: 8px; overflow: hidden; box-shadow: 0 8px 30px rgba(0,0,0,0.08); }
.video-wrapper iframe { position: absolute; top:0; left:0; width:100%; height:100%; }

/* Product controls */
.product-controls { display:flex; gap:1rem; align-items:center; margin:1.5rem 0 2rem; }
.product-controls input[type="search"] { flex:1; padding:0.8rem 1rem; border-radius:8px; border:1px solid #ddd; }
.product-controls select { padding:0.7rem 0.8rem; border-radius:8px; border:1px solid #ddd; background:#fff; }

/* Product grid (Shopee-style card) */
.product-grid { display:grid; grid-template-columns: repeat(4, 1fr); gap:1rem; }
.product-card { background: var(--white); border-radius:14px; overflow:hidden; box-shadow:0 10px 30px rgba(0,0,0,0.06); color:var(--primary-black); display:flex; flex-direction:column; position:relative; }
.product-card { transition: transform 0.25s ease, box-shadow 0.25s ease; }
.product-card:hover { transform: translateY(-6px); box-shadow: 0 18px 45px rgba(0,0,0,0.12); }
.product-thumb { display:none; }

/* Card UX inspired by reference */
.product-card .card__shine { position:absolute; inset:0; background:linear-gradient(120deg, rgba(255,255,255,0) 40%, rgba(255,255,255,0.8) 50%, rgba(255,255,255,0) 60%); opacity:0; transition: opacity 0.3s ease; pointer-events:none; }
.product-card .card__glow { position:absolute; inset:-10px; background:radial-gradient(circle at 50% 0%, rgba(88,194,52,0.12) 0%, rgba(88,194,52,0) 70%); opacity:0; transition: opacity 0.5s ease; pointer-events:none; }
.product-card .card__content { padding:1rem; height:100%; display:flex; flex-direction:column; gap:0.5rem; position:relative; z-index:2; }
.product-card .card__badge { position:absolute; top:12px; right:12px; background: var(--padi-green); color:#fff; padding:0.25em 0.5em; border-radius:999px; font-size:0.7em; font-weight:600; transform:scale(0.85); opacity:0; transition: all 0.4s ease 0.1s; }
.product-card .card__image { width:100%; height:140px; background-size:cover; background-position:center; border-radius:12px; transition: all 0.5s cubic-bezier(0.16,1,0.3,1); position:relative; overflow:hidden; }
.product-card .card__text { display:flex; flex-direction:column; gap:0.25em; }
.product-card .card__title { color: #111827; font-size:1rem; margin:0; font-weight:700; transition: all 0.3s ease; }
.product-card .card__description { color:#374151; font-size:0.85rem; margin:0; opacity:0.85; transition: all 0.3s ease; }
.product-card .card__footer { display:flex; justify-content:space-between; align-items:center; margin-top:auto; }
.product-card .card__price { color: #111827; font-weight:800; font-size:1rem; transition: all 0.3s ease; }
.product-card .card__button { width:32px; height:32px; background: var(--padi-green); border-radius:50%; display:flex; align-items:center; justify-content:center; color:#fff; cursor:pointer; transition: all 0.3s ease; transform: scale(0.95); }

/* hover */
.product-card:hover { transform: translateY(-12px); box-shadow: 0 28px 60px rgba(16, 185, 129, 0.08), 0 10px 20px rgba(0,0,0,0.08); border-color: rgba(88,194,52,0.12); }
.product-card:hover .card__shine { opacity:1; animation: shine 3s infinite; }
.product-card:hover .card__glow { opacity:1; }
.product-card:hover .card__badge { transform:scale(1); opacity:1; }
.product-card:hover .card__image { transform: translateY(-6px) scale(1.03); box-shadow: 0 12px 20px rgba(0,0,0,0.08); }
.product-card:hover .card__title { color: var(--padi-green); transform: translateX(2px); }
.product-card:hover .card__description { opacity:1; transform: translateX(2px); }
.product-card:hover .card__price { color: var(--padi-orange); transform: translateX(2px); }
.product-card:hover .card__button { transform: scale(1); box-shadow: 0 0 0 6px rgba(88,194,52,0.12); }
.product-body { padding:0.75rem 1rem; flex:1; display:flex; flex-direction:column; }
.product-title { font-weight:600; margin-bottom:0.25rem; font-size:0.95rem; }
.product-meta { font-size:0.9rem; color:rgba(0,0,0,0.6); margin-bottom:0.5rem; }
.product-footer { display:flex; align-items:center; justify-content:space-between; padding:0.75rem 1rem; border-top:1px solid #f0f0f0; }
.price { font-weight:700; color:var(--padi-orange); }
.whatsapp-cta { background:var(--padi-green); color:#fff; padding:0.5rem 0.75rem; border-radius:6px; text-decoration:none; font-weight:600; }
.whatsapp-cta:hover { transform: translateY(-3px); box-shadow: 0 12px 24px rgba(32,191,29,0.18); }

/* Porsche-like overlay effect for cards: on hover show translucent overlay and CTA */
.product-card { position: relative; overflow: hidden; }
.product-overlay { position: absolute; inset: 0; background: linear-gradient(180deg, rgba(0,0,0,0.0) 30%, rgba(0,0,0,0.6) 100%); opacity: 0; transition: opacity 0.35s ease; display:flex; align-items:flex-end; padding:1rem; }
.product-card:hover .product-overlay { opacity: 1; }
.product-overlay .overlay-meta { color: #fff; display:flex; align-items:center; justify-content:space-between; width:100%; }
.overlay-price { font-weight:800; font-size:1rem; color: var(--padi-orange); }
.overlay-cta { background: rgba(255,255,255,0.12); padding:0.5rem 0.75rem; border-radius:6px; color:#fff; text-decoration:none; font-weight:700; }
.overlay-cta:hover { background: rgba(255,255,255,0.18); }

/* Testimonials */
.testimonials-grid { display:grid; grid-template-columns: repeat(3,1fr); gap:1rem; }
.testimonial { background:#fff; padding:1rem; border-radius:8px; box-shadow:0 6px 20px rgba(0,0,0,0.05); color:var(--primary-black); }
/* testimonial slider styles */
.testimonial-slider { position: relative; display:flex; align-items:center; }
.slider-track { display:flex; gap:1rem; overflow:hidden; width:100%; }
.slider-track .testimonial { min-width: 100%; box-sizing:border-box; }
.slider-btn { background: rgba(0,0,0,0.05); border: none; width:44px; height:44px; border-radius:8px; cursor:pointer; font-size:1.5rem; display:flex; align-items:center; justify-content:center; }
.slider-btn:hover { background: rgba(0,0,0,0.08); }
.prev { margin-right: 1rem; }
.next { margin-left: 1rem; }

/* Footer */
.footer { background: #f8f8f8; padding:2.5rem 0; color:var(--primary-black); }
.footer-grid { display:grid; grid-template-columns: repeat(3,1fr); gap:1rem; }

/* WhatsApp floating button */
.whatsapp-float { position: fixed; right: 20px; bottom: 20px; width:56px; height:56px; border-radius:50%; display:flex; align-items:center; justify-content:center; box-shadow:0 10px 30px rgba(0,0,0,0.15); z-index:2000; background: linear-gradient(180deg,var(--padi-green) 0%, var(--padi-green-dark) 100%); color:#fff; text-decoration:none; transform-origin: center; }
.whatsapp-float:hover { transform: translateY(-6px) scale(1.03); box-shadow: 0 20px 40px rgba(0,0,0,0.18); }

/* Pulsing ring animation behind the button */
.whatsapp-float::after { content: ''; position: absolute; width: 56px; height: 56px; border-radius: 50%; left: 0; top: 0; box-shadow: 0 0 0 rgba(46, 203, 75, 0.5); animation: pulse 2.5s infinite; z-index: -1; }
@keyframes pulse {
  0% { transform: scale(0.9); opacity: 0.7; }
  70% { transform: scale(1.6); opacity: 0; }
  100% { transform: scale(1.6); opacity: 0; }
}

/* Subtle entrance animations */
.hero-content { animation: fadeInUp 1.2s ease-out both; }
.product-card { opacity: 0; transform: translateY(10px); animation: cardIn 0.6s ease forwards; }
@keyframes cardIn { to { opacity: 1; transform: translateY(0); } }


/* Responsive adjustments */
@media (max-width: 1024px) {
  .product-grid { grid-template-columns: repeat(3,1fr); }
}

@media (max-width: 768px) {
  .about-grid { grid-template-columns: 1fr; }
  .product-grid { grid-template-columns: repeat(2,1fr); }
  .testimonials-grid { grid-template-columns: 1fr; }
}

@media (max-width: 480px) {
  .product-grid { grid-template-columns: 1fr; }
  .product-controls { flex-direction:column; align-items:stretch; }
}

/* Responsive Design for Button Container */
@media (max-width: 768px) {
  .button-container {
    flex-direction: column;
    gap: 1rem;
  }

  .btn-left,
  .btn-right {
    flex: none;
    width: 100%;
  }
}